// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../app/(dashboard)/manager/page.tsx
{
  const handler = {} as typeof import("../../app/(dashboard)/manager/page.js")
  handler satisfies AppPageConfig<"/manager">
}

// Validate ../../app/auth/error/page.tsx
{
  const handler = {} as typeof import("../../app/auth/error/page.js")
  handler satisfies AppPageConfig<"/auth/error">
}

// Validate ../../app/auth/forgot-password/page.tsx
{
  const handler = {} as typeof import("../../app/auth/forgot-password/page.js")
  handler satisfies AppPageConfig<"/auth/forgot-password">
}

// Validate ../../app/auth/login/page.tsx
{
  const handler = {} as typeof import("../../app/auth/login/page.js")
  handler satisfies AppPageConfig<"/auth/login">
}

// Validate ../../app/auth/sign-up-success/page.tsx
{
  const handler = {} as typeof import("../../app/auth/sign-up-success/page.js")
  handler satisfies AppPageConfig<"/auth/sign-up-success">
}

// Validate ../../app/auth/sign-up/page.tsx
{
  const handler = {} as typeof import("../../app/auth/sign-up/page.js")
  handler satisfies AppPageConfig<"/auth/sign-up">
}

// Validate ../../app/auth/update-password/page.tsx
{
  const handler = {} as typeof import("../../app/auth/update-password/page.js")
  handler satisfies AppPageConfig<"/auth/update-password">
}

// Validate ../../app/page.tsx
{
  const handler = {} as typeof import("../../app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../app/protected/page.tsx
{
  const handler = {} as typeof import("../../app/protected/page.js")
  handler satisfies AppPageConfig<"/protected">
}

// Validate ../../app/auth/confirm/route.ts
{
  const handler = {} as typeof import("../../app/auth/confirm/route.js")
  handler satisfies RouteHandlerConfig<"/auth/confirm">
}





// Validate ../../app/layout.tsx
{
  const handler = {} as typeof import("../../app/layout.js")
  handler satisfies LayoutConfig<"/">
}
