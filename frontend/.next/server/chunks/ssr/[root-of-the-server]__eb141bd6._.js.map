{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Logivance/cortexa/frontend/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\n/**\n * If using Fluid compute: Don't put this client in a global variable. Always create a new client within each\n * function when using it.\n */\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAMO,eAAe;IACpB,MAAM,cAAc,MAAM,IAAA,0IAAO;IAEjC,OAAO,IAAA,+LAAkB,oKAGvB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Logivance/cortexa/frontend/app/%28dashboard%29/manager/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/server'\n\nexport default async function ManagerDashboardPage() {\n  const supabase = await createClient()\n\n  const { data, error } = await supabase.auth.getClaims()\n  if (error || !data?.claims) {\n    redirect('/auth/login')\n  }\n\n  return (\n    <>\n      <h1>Manager Dashboard</h1>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,IAAA,yIAAY;IAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS;IACrD,IAAI,SAAS,CAAC,MAAM,QAAQ;QAC1B,IAAA,iMAAQ,EAAC;IACX;IAEA,qBACE;kBACE,cAAA,8OAAC;sBAAG;;;;;;;AAGV", "debugId": null}}]}